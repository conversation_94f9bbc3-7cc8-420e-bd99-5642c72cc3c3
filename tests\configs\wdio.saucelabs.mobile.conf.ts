import type { Options } from '@wdio/types';
import { join } from 'path';
import { config as sauceSharedConfig } from './wdio.saucelabs.shared.conf.ts';
import { getStepDefinitionFiles } from '../support/helpers/step-definition-finder.ts';

const build = `WebdriverIO - Cucumber - Demo - ${new Date().getTime()}`;

export const config: Options.Testrunner = {
  ...sauceSharedConfig,
  automationProtocol: 'webdriver',
  capabilities: [
    {
      'appium:deviceName': 'iPhone.*',
      'appium:automationName': 'XCUITest',
      'appium:platformVersion': '18.*.*',
      'appium:autoAcceptAlerts': false,
      browserName: 'Safari',
      platformName: 'iOS',
      'sauce:options': {
        build,
        appiumVersion: 'stable',
        maxDuration: 3600,
        idleTimeout: 180,
        commandTimeout: 180,
      },
      webSocketUrl: true,
    },
    // {
    //   // iPhone 15 - Another latest real iOS device
    //   'appium:deviceName': 'iPhone 15',
    //   'appium:automationName': 'XCUITest',
    //   browserName: 'Safari',
    //   platformName: 'iOS',
    //   'sauce:options': {
    //     appiumVersion: 'latest',
    //     build,
    //   },
    // },
    //  {
    //   // Samsung Galaxy S24 Ultra - Latest real Android device

    //   'appium:deviceName': 'Samsung.*',
    //   'appium:automationName': 'UiAutomator2',
    //   'appium:platformVersion': '15',
    //   browserName: 'Chrome',
    //   platformName: 'Android',
    //   'sauce:options': {
    //     appiumVersion: 'stable',
    //     build,

    //   },
    //    // Chrome-specific capabilities at the root level
    //    'goog:chromeOptions': {
    //     args: [
    //         '--start-maximized',
    //         '--disable-notifications',
    //         '--disable-popup-blocking'
    //     ]
    // },
    //   webSocketUrl: false,
    //   'appium:resetKeyboard':true,
    //   'appium:unicodeKeyboard':true
    // },
    // {
    //   // Google Pixel 8 Pro - Latest real Android device
    //   "appium:deviceName": "Google.*",
    //   'appium:platformVersion': '15',
    //   "appium:automationName": "UiAutomator2",
    //   browserName: "Chrome",
    //   platformName: "Android",
    //   "sauce:options": {
    //     appiumVersion: "latest",
    //     build,
    //   },
    //   webSocketUrl: false,
    //   unhandledPromptBehavior:'dismiss'
    // },
  ],

  // Only include the specific feature file that contains the tagged scenario
  specs: [join(process.cwd(), './tests/features/sensa/mobile-forgotPasswordpage.feature')],

  // Add these important configurations
  services: [
    [
      'sauce',
      {
        region: 'us-west-1',
      },
    ],
    [
      'gmail',
      {
        credentials: join(
          process.cwd(),
          'tests/resources/google-key/gmailCredentials.json',
        ),
        token: join(process.cwd(), 'tests/resources/google-key/token.json'),
        intervalSec: 10,
        timeoutSec: 60,
      },
    ],
  ],

  // Increase global timeouts
  maxInstances: 1, // Reduce concurrent sessions for stability
  waitforTimeout: 180000,
  connectionRetryTimeout: 180000,
  connectionRetryCount: 3,
  framework: 'cucumber',
  specFileRetries: 0,
  specFileRetriesDelay: 0,
  specFileRetriesDeferred: false,

  // Cucumber specific configuration
  cucumberOpts: {
    require: ['./tests/step-definitions/sensa/*.ts'],
    backtrace: false,
    requireModule: ['ts-node/register'],
    dryRun: false,
    failFast: false,
    colors: true,
    snippets: true,
    source: true,
    strict: false,
    tags: '@SensaForgotPasswordFlowfromForgotUsernamePage_Validation_QA',
    timeout: 1600000,
    ignoreUndefinedDefinitions: false,
    retry: 1, // Disable cucumberOpts retry, use CLI --retry if needed
    retryTagFilter: '', // Only retry scenarios tagged with @flaky
    scenarioLevelReporter: true,
    format: [
      'pretty',
      'json:reports/cucumber-report.json',
      'rerun:reports/rerun.txt', // This creates a rerun file
      'summary', // Add summary format for better scenario reporting
      'html:reports/cucumber-report.html',
      'junit:reports/cucumber-report.xml',
      'usage:reports/cucumber-report.usage.json',
      'message:reports/cucumber-report.ndjson',
      'progress-bar',      
    ],
  },
};
