import logger from './logger.util.ts';
import '../types/gmail-service.d.ts';

/**
 * Interface for email filter criteria
 */
export interface EmailFilters {
    from?: string;
    to?: string;
    subject?: string;
    includeBody?: boolean;
    includeAttachments?: boolean;
    before?: Date;
    after?: Date;
    label?: string;
}

/**
 * Interface for email object structure
 */
export interface EmailObject {
    from: string;
    to?: string;
    subject: string;
    date: string | Date;
    body?: {
        html?: string;
        text?: string;
    };
    attachments?: Array<{
        filename: string;
        content: string;
        contentType: string;
    }>;
}

/**
 * Gmail utility class for email operations using wdio-gmail-service
 */
class GmailUtil {
    auth: any;

    /**
     * Check inbox for emails matching the given criteria
     * @param filters Email filter criteria
     * @returns Array of emails matching the criteria
     */
    public async checkInbox(filters: EmailFilters): Promise<EmailObject[]> {
        try {
            logger.info(`Checking Gmail inbox with filters: ${JSON.stringify(filters)}`);
            // Use the Gmail service provided by wdio-gmail-service
            const emails = await browser.checkInbox(filters);
            // Handle null response from Gmail service
            const emailList = (emails || []) as EmailObject[];
            logger.info(`Found ${emailList.length} emails matching the criteria`);
            return emailList;
        } catch (error) {
            logger.error(`Error checking Gmail inbox: ${error}`);
            throw error;
        }
    }

    /**
     * Wait for an email to arrive with specific criteria
     * @param filters Email filter criteria
     * @param timeoutMs Maximum time to wait in milliseconds
     * @param intervalMs Check interval in milliseconds
     * @returns First email matching the criteria
     */
    /**
     * Utility function to create a delay
     * @param ms Milliseconds to wait
     */
    private async delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    public async waitForEmail(
        filters: EmailFilters,
        timeoutMs: number = 60000,
        intervalMs: number = 5000,
    ): Promise<EmailObject> {
        const startTime = Date.now();

        while (Date.now() - startTime < timeoutMs) {
            try {
                const emails = await this.checkInbox(filters);
                if (emails.length > 0) {
                    logger.info(`Email found after ${Date.now() - startTime}ms`);
                    return emails[0];
                }

                logger.info(`No email found yet, waiting ${intervalMs}ms before next check...`);
                await this.delay(intervalMs);
            } catch (error) {
                logger.info(`Error during email check: ${error}`);
                await this.delay(intervalMs);
            }
        }

        throw new Error(`No email found matching criteria after ${timeoutMs}ms timeout`);
    }

    /**
     * Get the latest email from a specific sender
     * @param fromEmail Sender's email address
     * @param includeBody Whether to include email body
     * @returns Latest email from the sender
     */
    public async getLatestEmailFrom(fromEmail: string, includeBody: boolean = true): Promise<EmailObject> {
        logger.info(`Getting latest email from: ${fromEmail}`);

        const emails = await this.checkInbox({
            from: fromEmail,
            includeBody: includeBody,
        });

        if (emails.length === 0) {
            throw new Error(`No emails found from ${fromEmail}`);
        }

        // Sort by date (most recent first) and return the latest
        const sortedEmails = emails.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        return sortedEmails[0];
    }

    /**
     * Search for emails containing specific text in subject
     * @param subjectText Text to search for in subject
     * @param includeBody Whether to include email body
     * @returns Array of emails with matching subject
     */
    public async searchEmailsBySubject(subjectText: string, includeBody: boolean = true): Promise<EmailObject[]> {
        logger.info(`Searching emails with subject containing: ${subjectText}`);

        return await this.checkInbox({
            subject: subjectText,
            includeBody: includeBody,
        });
    }

    /**
 * Search for emails containing specific text in subject
 * @param subjectText Text to search for in subject
 * @param includeBody Whether to include email body
 * @returns Array of emails with matching subject
 */
    public async searchLatestEmailBySubject(subjectText: string, includeBody: boolean = true): Promise<EmailObject> {
        logger.info(`Searching emails with subject containing: ${subjectText}`);

        const emails= await this.checkInbox({
            subject: subjectText,
            includeBody: includeBody,
        });

        if (emails.length === 0) {
            throw new Error(`No emails found with subject containing: ${subjectText}`);
        }

        // Sort by date (most recent first) and return the latest
        const sortedEmails = emails.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        return sortedEmails[0];
    }
    /**
     * Verify email content contains expected text
     * @param email Email object
     * @param expectedText Text to search for
     * @param searchInHtml Whether to search in HTML body (default: true)
     * @returns True if text is found
     */
    public verifyEmailContent(email: EmailObject, expectedText: string, searchInHtml: boolean = true): boolean {
        if (!email.body) {
            logger.info('Email body not available for content verification');
            return false;
        }

        const contentToSearch = searchInHtml ? email.body.html : email.body.text;
        const found = Boolean(contentToSearch && contentToSearch.includes(expectedText));

        logger.info(`Email content verification: ${found ? 'PASSED' : 'FAILED'}`);
        logger.info(`Searching for: "${expectedText}"`);

        return found;
    }

    /**
     * Extract links from email HTML body
     * @param email Email object
     * @returns Array of URLs found in the email
     */
    public extractLinksFromEmail(email: EmailObject): string[] {
        if (!email.body || !email.body.html) {
            logger.info('Email HTML body not available for link extraction');
            return [];
        }

        const htmlContent = email.body.html;
        const linkRegex = /href=["'](https?:\/\/[^"']+)["']/gi;
        const links: string[] = [];
        let match;

        while ((match = linkRegex.exec(htmlContent)) !== null) {
            links.push(match[1]);
        }

        logger.info(`Extracted ${links.length} links from email`);
        return links;
    }

    /**
     * Get email count for specific criteria
     * @param filters Email filter criteria
     * @returns Number of emails matching the criteria
     */
    public async getEmailCount(filters: EmailFilters): Promise<number> {
        const emails = await this.checkInbox(filters);
        logger.info(`Email count for given criteria: ${emails.length}`);
        return emails.length;
    }


    /**
    * Click on a link in the email body with specified link text
    * @param email Email object
    * @param linkText Text of the link to click
    */

    public async clickOnLinkInEmail(email: EmailObject, linkText: string): Promise<void> {
        if (!email.body || !email.body.html) {
            logger.info('Email HTML body not available for link extraction');
            console.log('Email HTML body not available for link extraction');
            return;
        }
        const htmlContent = email.body.html;
        // Use a more robust regex to capture the href attribute and link text
        const linkRegex = /<a[^>]*href=["']([^"']+)["'][^>]*>(.*?)<\/a>/gi;
        let match;
        let linkToClick: string | null = null;
        // Search for the link with the specified text
        while ((match = linkRegex.exec(htmlContent)) !== null) {
            const href = match[1].trim();      // The URL
            const anchorText = match[2].trim().toLowerCase(); // The visible text in <a>
            console.log(`Found link: Text="${anchorText}" URL=${href}`);

            if (anchorText.toLowerCase() === (linkText.toLowerCase())) {
                linkToClick = href;
                console.log(`Extracted link successfully: ${linkToClick}`);
                break;
            }

        }
        if (linkToClick) {
            console.log(`Navigating to link: ${linkToClick}`);
            logger.info(`Navigating to link: ${linkToClick}`);
            await browser.url(linkToClick);
        } else {
            logger.info(`No link with text "${linkText}" found in the email.`);
        }


    }

}


export default GmailUtil;
